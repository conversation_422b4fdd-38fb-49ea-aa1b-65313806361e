/**
 * Products Types
 *
 * This file defines the TypeScript interfaces for the products feature.
 */

// Backend API response wrapper
export interface ApiResponseWrapper<T> {
  success: boolean;
  message: string;
  data: T;
}

// Backend pagination response (matches actual API response)
export interface PaginationInfo {
  currentPage: number;
  totalPages: number;
  totalItems: number;
  itemsPerPage: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

// Product attribute types (frontend)
export interface ProductAttribute {
  id: number;
  key: string;
  value: string;
}

// Product variant types (frontend)
export interface ProductVariant {
  id: number;
  name: string;
  type: string;
  price: number;
  stock: number;
}

// Backend category interface
export interface BackendCategory {
  id: number;
  name: string;
  description: string;
}

// Backend supplier interface
export interface BackendSupplier {
  id: string;
  name: string;
  email: string;
  phone: string;
}

// Backend customer interface
export interface BackendCustomer {
  id: string;
  name: string;
  email: string;
}

// Backend review interface
export interface BackendReview {
  id: number;
  rating: number;
  comment: string;
  customerName: string;
  createdAt: string;
}

// Backend attribute interface (matches actual API response)
export interface BackendProductAttribute {
  id: number;
  key: string;
  value: string;
}

// Backend variant interface (matches actual API response)
export interface BackendProductVariant {
  id: number;
  name: string;
  type: string;
  price: number;
  stock: number;
}

// Backend product interface matching actual API specification
export interface BackendProduct {
  id: number;                      // Backend uses number IDs
  name: string;                    // Backend uses lowercase
  description: string;             // Backend uses lowercase
  price: number;                   // Backend uses lowercase
  stock: number;                   // Backend uses lowercase
  minimumStock: number;            // Backend uses camelCase
  sku: string;                     // Backend uses lowercase (auto-generated)
  categoryId: number;              // Backend uses categoryId (number)
  supplierId: string;              // Backend uses supplierId (GUID)
  customerId: string | null;       // Backend includes customerId
  image: string;                   // Backend uses single image URL
  images: string[];                // Backend uses array of image URLs
  category: BackendCategory;       // Backend includes full category object
  supplier: BackendSupplier;       // Backend includes full supplier object
  customer: BackendCustomer | null; // Backend includes full customer object
  attributes: BackendProductAttribute[]; // Backend includes attributes array
  variants: BackendProductVariant[]; // Backend includes variants array
  reviews: BackendReview[];        // Backend includes reviews array
  createdAt: string;               // Backend uses camelCase
  updatedAt: string | null;        // Backend uses camelCase, nullable
}

// Frontend product interface (for UI compatibility)
export interface Product {
  id: string;
  name: string;
  sku: string;
  category: string;
  price: number;
  stock: number;
  minimumStock: number;
  status: 'active' | 'inactive' | 'out_of_stock';
  description?: string | undefined;
  image?: string | undefined;      // Primary image
  images?: string[] | undefined;   // All images
  supplierId: string;
  attributes?: ProductAttribute[] | undefined;
  variants?: ProductVariant[] | undefined;
  createdAt: string;
  updatedAt: string;
}

// Form data interface for creating/updating products
export interface ProductFormData {
  name: string;
  sku: string;
  category: string;
  price: number;
  stock: number;
  minimumStock: number;
  status: 'active' | 'inactive' | 'out_of_stock';
  description?: string | undefined;
  supplierId: string;
  attributes?: ProductAttribute[] | undefined;
  variants?: ProductVariant[] | undefined;
  // Images handled separately through upload endpoint
}

// Frontend form data that allows File objects for images
export interface ProductFormDataWithImages extends Omit<ProductFormData, 'images'> {
  images?: (File | string)[];
}

// Backend API query parameters for products
export interface ProductQueryParams {
  page?: number;                   // Page number (default: 1)
  limit?: number;                  // Items per page (default: 20, max: 100)
  search?: string;                 // Search in product name and SKU
  category?: string;               // Filter by category
  status?: 'active' | 'inactive' | 'out_of_stock';  // Filter by status
  supplierId?: string;             // Filter by supplier
  minPrice?: number;               // Minimum price filter
  maxPrice?: number;               // Maximum price filter
  inStock?: boolean;               // Filter products in stock
  sort?: 'Name' | 'SKU' | 'Price' | 'Stock' | 'CreatedAt' | 'UpdatedAt';  // Sort field
  order?: 'asc' | 'desc';          // Sort order
}

// Backend products list response
export interface ProductsListResponse extends ApiResponseWrapper<BackendProduct[]> {
  pagination?: PaginationInfo;
}

// Image upload response
export interface ImageUploadResponse {
  success: boolean;
  message: string;
  imageUrls: string[];
}

// Product status update
export interface ProductStatusUpdate {
  status: 'active' | 'inactive' | 'out_of_stock';
}

// Product analytics data
export interface ProductAnalyticsData {
  totalProducts: number;
  activeProducts: number;
  inactiveProducts: number;
  outOfStockProducts: number;
  lowStockProducts: number;
  averagePrice: number;
  totalValue: number;
  topCategories: Array<{
    category: string;
    count: number;
    percentage: number;
  }>;
  recentProducts: Product[];
}

// Note: All types are already exported above with their declarations
